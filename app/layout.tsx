import "./globals.css";
import Script from "next/script";
import type { Metadata } from "next";
import localFont from "next/font/local";
import { Toaster } from "@/components/ui/toaster";
import { CartProvider } from "@/lib/CartContext";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "MailPallet - Shop UK, Deliver to Africa",
  description:
    "MailPallet: Your ultimate UK shopping and Africa delivery solution. Shop from top UK retailers like Amazon, ASOS, and eBay with a free UK address. We offer fast, reliable international shipping to Nigeria, Ghana, Kenya, and more African countries. Save up to 90% on shipping costs with our package consolidation service. Enjoy secure payments, real-time tracking, and dedicated customer support. Experience hassle-free online shopping from the UK with doorstep delivery across Africa. Start your global shopping journey with MailPallet today!",
  openGraph: {
    title: "MailPallet - Your UK Shopping Gateway to Africa",
    description:
      "Shop in the UK with MailPallet and get your items delivered to Africa. Free UK address, easy shipping, and great savings up to 15%!",
  },
  twitter: {
    title: "Shop UK, Deliver to Africa with MailPallet",
    description:
      "Shop in the UK with MailPallet and get your items delivered to Africa. Free UK address, easy shipping, and great savings up to 15%!",
    creator: "@mailpallet",
    site: "https://mailpallet.com",
  },
  keywords:
    "uk shopping, africa delivery, mailpallet, international shipping, online shopping, easy shipping, package forwarding",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="manifest" href="/manifest.json" />

        <link
          rel="icon"
          type="image/png"
          sizes="196x196"
          href="/favicon-196.png"
        />

        <link rel="apple-touch-icon" href="/apple-icon-180.png" />

        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-HHS9KK75XH"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-HHS9KK75XH');
          `}
        </Script>
        <Script id="console-suppression" strategy="afterInteractive">
          {`
            if (!window.location.href.includes('localhost')) {
              console.log = () => {};
              console.error = () => {};
              console.warn = () => {};
            }
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <CartProvider>
          {children}
          <SpeedInsights />
          <Analytics />
          <Toaster />
        </CartProvider>
      </body>
    </html>
  );
}
